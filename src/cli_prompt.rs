use crate::app::App;
use crate::llm_client;
use reedline::{Prompt, PromptEditMode, PromptHistorySearch, PromptHistorySearchStatus};
use std::borrow::Cow;
use std::sync::{Arc, Mutex};
use tiktoken_rs::{cl100k_base, CoreBPE};

#[derive(<PERSON><PERSON><PERSON>, <PERSON>lone)]
pub struct CurrentPromptBufferState {
    pub current_buffer_content: String,
}

#[derive(Default)]
pub struct TokenCache {
    pub system_prompt_tokens: usize,
    pub history_tokens: usize,
    pub history_version: usize, // Track when history changes
}

#[derive(Clone)]
pub struct TokenCountingPrompt {
    app_arc: Arc<Mutex<App>>,
    prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
    tokenizer: Arc<CoreBPE>,
    token_cache: Arc<Mutex<TokenCache>>,
}

impl TokenCountingPrompt {
    pub fn update_buffer_content(&self, content: String) {
        if let Ok(mut buffer_state) = self.prompt_buffer_state.lock() {
            buffer_state.current_buffer_content = content;
        }
    }
}

impl TokenCountingPrompt {
    pub fn new(
        app_arc: Arc<Mutex<App>>,
        prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
    ) -> Self {
        let tokenizer = Arc::new(cl100k_base().unwrap_or_else(|e| {
            eprintln!(
                "Failed to load cl100k_base tokenizer: {}. Token counting will be inaccurate.",
                e
            );
            panic!("Tokenizer cl100k_base failed to load: {}", e);
        }));

        // Pre-calculate system prompt tokens since it never changes
        let system_prompt_tokens = tokenizer
            .encode_with_special_tokens(llm_client::SYSTEM_PROMPT)
            .len();

        let token_cache = Arc::new(Mutex::new(TokenCache {
            system_prompt_tokens,
            history_tokens: 0,
            history_version: 0,
        }));

        Self {
            app_arc,
            prompt_buffer_state,
            tokenizer,
            token_cache,
        }
    }
}

impl Prompt for TokenCountingPrompt {
    fn render_prompt_left(&self) -> Cow<str> {
        Cow::Owned("".to_string())
    }

    fn render_prompt_right(&self) -> Cow<str> {
        Cow::Borrowed("")
    }

    fn render_prompt_indicator(&self, _edit_mode: PromptEditMode) -> Cow<str> {
        Cow::Borrowed("> ")
    }

    fn render_prompt_multiline_indicator(&self) -> Cow<str> {
        Cow::Borrowed("  ")
    }

    fn render_prompt_history_search_indicator(
        &self,
        history_search: PromptHistorySearch,
    ) -> Cow<str> {
        let prefix = match history_search.status {
            PromptHistorySearchStatus::Passing => "",
            PromptHistorySearchStatus::Failing => "failing ",
        };
        Cow::Owned(format!(
            "({}reverse-search: {}) ",
            prefix, history_search.term
        ))
    }

    fn right_prompt_on_last_line(&self) -> bool {
        true
    }
}


