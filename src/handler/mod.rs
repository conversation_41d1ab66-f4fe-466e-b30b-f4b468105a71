pub mod tool_executor;
pub mod file_tools;
pub mod directory_tools;
pub mod search_tools;
pub mod metadata_tools;
pub mod edit_tools;

use crate::{
    app::{App, AppMessage, MessageContent},
    display, llm_client, session_manager,
};
use crossterm::style::Stylize; // For .yellow(), .bold() etc.
use reqwest::Client;
use std::{
    io::{self, Write},
    path::Path,
    sync::{Arc, Mutex},
};

pub async fn process_user_input(
    buffer: String,
    app_arc: Arc<Mutex<App>>,
    http_client: &Client,
    session_file_path: &Path,
) -> Result<(), Box<dyn std::error::Error>> {
    // 1. Create user AppMessage, display it, add to app.messages, add to llm_history
    let user_app_message = AppMessage {
        sender: "User".to_string(),
        parts: vec![MessageContent::Text(buffer.clone())],
    };
    display::print_formatted_message(&user_app_message)?;

    {
        // Lock scope for app update
        let mut app_locked = app_arc.lock().unwrap();
        app_locked.messages.push(user_app_message.clone());
        app_locked.add_user_message_to_llm_history(buffer.clone());
        if let Err(e) =
            session_manager::save_session(session_file_path, &app_locked.conversation_history_for_llm)
        {
            eprintln!("{}", format!("Error saving session: {}", e).red());
        }
    } // Lock released

    let mut max_tool_iterations = 5; // Prevent infinite tool call loops

    'tool_loop: loop {
        if max_tool_iterations == 0 {
            let err_msg_text = "Max tool iterations reached. Aborting sequence.".to_string();
            let err_msg = AppMessage {
                sender: "System".to_string(),
                parts: vec![MessageContent::Text(err_msg_text.clone())],
            };
            display::print_formatted_message(&err_msg)?;
            {
                // Lock scope
                let mut app_locked = app_arc.lock().unwrap();
                app_locked.messages.push(err_msg);
                // Optionally add this system error to LLM history if it should know
                // app_locked.add_tool_response_to_llm_history("system_error", err_msg_text);
            } // Lock released
            break 'tool_loop;
        }
        max_tool_iterations -= 1;

        let messages_for_api: Vec<llm_client::ChatMessage>;
        {
            // Lock scope for reading history
            let app_locked = app_arc.lock().unwrap();
            let mut constructed_messages = vec![llm_client::ChatMessage {
                role: "system".to_string(),
                content: llm_client::SYSTEM_PROMPT.to_string(),
            }];
            constructed_messages.extend_from_slice(&app_locked.conversation_history_for_llm);
            messages_for_api = constructed_messages;
        } // Lock released

        println!("{}", "AI is thinking...".italic().yellow());
        io::stdout().flush()?; // Ensure "thinking" message is displayed

        // Capture the start time for potential retry messages
        let llm_result = llm_client::call_llm_api(http_client, messages_for_api).await;

        match llm_result {
            Ok(response_content) => {
                let ai_app_message_for_display: AppMessage;
                let mut should_continue_tool_loop 
                {
                    // Scope for app_arc lock
                    let mut app_locked = app_arc.lock().unwrap();
                    // app_locked.llm_is_thinking = false; // Not strictly needed for CLI if "thinking" is just printed
                    app_locked.add_assistant_response_to_llm_history(response_content.clone());
                    ai_app_message_for_display =
                        app_locked.create_ai_app_message_from_raw(&response_content);
                    app_locked.messages.push(ai_app_message_for_display.clone());

                    if let Some(MessageContent::ToolCall(tool_call_action)) =
                        ai_app_message_for_display.parts.first()
                    {
                        // --- Tool Call Execution Logic ---
                        drop(app_locked); // Release lock before tool operations

                        should_continue_tool_loop = tool_executor::execute_tool(
                            tool_call_action,
                            app_arc.clone(),
                            &ai_app_message_for_display,
                        ).await?;
                    } else {
                        // No tool call, just display the AI message
                        drop(app_locked); // Release lock
                        display::print_formatted_message(&ai_app_message_for_display)?;
                        should_continue_tool_loop = false;
                    }
                }

                { // Scope for final save in this iteration of tool_loop
                    let app_locked = app_arc.lock().unwrap();
                    if let Err(e) = session_manager::save_session(
                        session_file_path,
                        &app_locked.conversation_history_for_llm,
                    ) {
                        eprintln!("{}", format!("Error saving session: {}", e).red());
                    }
                } // Lock released

                if should_continue_tool_loop {
                    continue 'tool_loop;
                } else {
                    break 'tool_loop;
                }
            }
            Err(e) => {
                let system_error_msg = AppMessage {
                    sender: "System".to_string(),
                    parts: vec![MessageContent::Text(format!("Error from LLM: {}", e))],
                };
                display::print_formatted_message(&system_error_msg)?;
                { // Lock scope
                    app_arc.lock().unwrap().messages.push(system_error_msg);
                } // Lock released
                break 'tool_loop; // Break on LLM error
            }
        }
    } // End 'tool_loop

    Ok(())
}
