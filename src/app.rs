use crate::llm_client::{try_parse_tool_call, ChatMessage, ToolCallAction};
use pulldown_cmark::{CodeBlockKind, Event, Parser, Tag, TagEnd};

#[derive(Clone, Debug, PartialEq)]
pub enum MessageContent {
    Text(String),
    CodeBlock {
        language: Option<String>,
        content: String,
    },
    ToolCall(ToolCallAction),
}

#[derive(<PERSON><PERSON>, Debug)]
pub struct AppMessage {
    pub sender: String,
    pub parts: Vec<MessageContent>,
}

pub struct App {
    #[allow(dead_code)]
    pub http_client: reqwest::Client, // Used in handler.rs but passed separately
    pub messages: Vec<AppMessage>, // For display structuring and logging
    pub conversation_history_for_llm: Vec<ChatMessage>, // For actual LLM context
    pub should_quit: bool,
}

impl App {
    pub fn new(http_client: reqwest::Client) -> Self {
        App {
            http_client,
            messages: Vec::new(),
            conversation_history_for_llm: Vec::new(),
            should_quit: false,
        }
    }

    pub fn add_user_message_to_llm_history(&mut self, content: String) {
        self.conversation_history_for_llm.push(ChatMessage {
            role: "user".to_string(),
            content,
        });
    }

    pub fn add_assistant_response_to_llm_history(&mut self, raw_content: String) {
        // If the raw_content is a JSON representation of a tool call, store that.
        // Otherwise, store the raw text/markdown.
        // This aligns with OpenAI: assistant message can have `content` or `tool_calls`.
        // We simplify by putting the tool_call JSON string *into* content if it's a tool call.
        let content_for_llm = if let Some(tool_call) = try_parse_tool_call(&raw_content) {
            // Store the JSON string of the tool call action
            serde_json::to_string(&tool_call).unwrap_or_else(|_| raw_content.clone())
        } else {
            raw_content
        };

        self.conversation_history_for_llm.push(ChatMessage {
            role: "assistant".to_string(),
            content: content_for_llm,
        });
    }

    pub fn add_tool_response_to_llm_history(&mut self, _tool_name: &str, response_content: String) {
        // _tool_name is not used if ChatMessage doesn't have a 'name' field for 'tool' role.
        // The LLM knows which tool was called from the preceding assistant message.
        self.conversation_history_for_llm.push(ChatMessage {
            role: "tool".to_string(),  // Standard OpenAI role for tool results
            content: response_content, // The textual output from the tool
        });
    }

    pub fn create_ai_app_message_from_raw(&self, raw_content: &str) -> AppMessage {
        // This function parses the AI's raw response string into structured AppMessage for display.
        // It checks if the response is a tool call or regular text/markdown.
        if let Some(tool_call) = try_parse_tool_call(raw_content) {
            AppMessage {
                sender: "AI".to_string(),
                parts: vec![MessageContent::ToolCall(tool_call.clone())],
            }
        } else {
            // Check if the content contains code blocks - if not, treat as plain text
            if !raw_content.contains("```") {
                // Simple text content - preserve newlines exactly as they are
                return AppMessage {
                    sender: "AI".to_string(),
                    parts: vec![MessageContent::Text(raw_content.to_string())],
                };
            }

            // Parse markdown for text and code blocks
            let mut parts = Vec::new();
            let mut current_text = String::new();
            let parser = Parser::new(raw_content);
            let mut in_code_block_lang: Option<String> = None;

            for event in parser {
                match event {
                    Event::Start(Tag::CodeBlock(CodeBlockKind::Fenced(lang))) => {
                        if !current_text.is_empty() {
                            parts.push(MessageContent::Text(current_text.clone()));
                            current_text.clear();
                        }
                        let lang_str = lang.into_string();
                        in_code_block_lang = Some(lang_str.clone());
                        parts.push(MessageContent::CodeBlock {
                            language: Some(lang_str),
                            content: String::new(),
                        });
                    }
                    Event::End(TagEnd::CodeBlock) => {
                        in_code_block_lang = None;
                    }
                    Event::Text(text_cow) => {
                        if in_code_block_lang.is_some() {
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push_str(&text_cow);
                            }
                        } else {
                            current_text.push_str(&text_cow);
                        }
                    }
                    Event::HardBreak | Event::SoftBreak => {
                        if in_code_block_lang.is_some() {
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push('\n');
                            }
                        } else {
                            current_text.push('\n');
                        }
                    }
                    Event::Html(html_cow) => {
                        // Treat HTML as text
                        if in_code_block_lang.is_some() {
                            if let Some(MessageContent::CodeBlock { content, .. }) =
                                parts.last_mut()
                            {
                                content.push_str(&html_cow);
                            }
                        } else {
                            current_text.push_str(&html_cow);
                        }
                    }
                    // Ignoring other Markdown tags like Emphasis, Strong, List, etc. for simplicity in CLI.
                    // They will be part of `current_text` if not handled specifically.
                    _ => {}
                }
            }
            if !current_text.is_empty() {
                parts.push(MessageContent::Text(current_text.clone()));
            }

            // Fallback if parsing results in no parts but raw_content is not empty
            if parts.is_empty() && !raw_content.is_empty() {
                parts.push(MessageContent::Text(raw_content.to_string()));
            }

            AppMessage {
                sender: "AI".to_string(),
                parts,
            }
        }
    }
}
