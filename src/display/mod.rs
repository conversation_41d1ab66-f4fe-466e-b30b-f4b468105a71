// Re-export all display functionality
pub mod ai_message;
pub mod common;
pub mod system_message;
pub mod user_message;

use crate::app::AppMessage;
pub use common::truncate_tool_output;
use std::io;

pub fn print_formatted_message(app_msg: &AppMessage) -> io::Result<()> {
    match app_msg.sender.as_str() {
        "User" => user_message::print_user_message(app_msg),
        "AI" => ai_message::print_ai_message(app_msg),
        _ => system_message::print_system_message(app_msg),
    }
}
