use crate::app::{AppMessage, MessageContent};
use crate::syntax_highlighting;
use console::measure_text_width;
use crossterm::style::{Color, Print, ResetColor, SetBackgroundColor, SetForegroundColor, Stylize};
use std::io::{self, stdout, Write};
// For measuring visible width of text with ANSI codes
use textwrap::{Options, WordSplitter};
use regex::Regex;
use crate::display::colors::{BOLD_COLOR, CODE_BLOCK_BACKGROUND, INLINE_CODE_COLOR};

#[derive(Debug)]
enum ProcessedLine {
    Heading { content: String, centered: bool },
    Regular { content: String },
}

fn process_markdown_text(text: &str, _max_width: u16) -> Vec<ProcessedLine> {
    let mut result = Vec::new();

    // Regex patterns for markdown elements
    let heading_regex = Regex::new(r"^(#{1,6})\s+(.+)$").unwrap();
    let bold_regex = Regex::new(r"\*\*([^*]+)\*\*").unwrap();
    let inline_code_regex = Regex::new(r"`([^`]+)`").unwrap();
    let list_item_regex = Regex::new(r"^(\s*)-\s+(.+)$").unwrap();

    for line in text.lines() {
        let trimmed = line.trim();

        // Check if it's a heading
        if let Some(captures) = heading_regex.captures(trimmed) {
            let level = captures.get(1).unwrap().as_str().len();
            let heading_text = captures.get(2).unwrap().as_str();

            // Style the heading based on level - use colors instead of bold
            let styled_heading = match level {
                1 => format!("{}{}{}",
                    SetForegroundColor(Color::Cyan),
                    heading_text,
                    ResetColor
                ),
                2 => format!("{}{}{}",
                    SetForegroundColor(Color::Blue),
                             heading_text,
                    ResetColor
                ),
                3 => format!("{}{}{}",
                    SetForegroundColor(Color::Magenta),
                             heading_text,
                    ResetColor
                ),
                _ => heading_text.into()
            };

            // Add spacing before headings (except for the first line)
            if !result.is_empty() {
                result.push(ProcessedLine::Regular {
                    content: String::new(),
                });
            }

            result.push(ProcessedLine::Heading {
                content: styled_heading,
                centered: level <= 3, // Center h1, h2, h3
            });

            // Add spacing after headings
            result.push(ProcessedLine::Regular {
                content: String::new(),
            });
        } else if let Some(captures) = list_item_regex.captures(line) {
            // Handle list items with better formatting
            let indent = captures.get(1).unwrap().as_str();
            let item_text = captures.get(2).unwrap().as_str();

            let mut processed_item = item_text.to_string();
            processed_item = bold_regex.replace_all(&processed_item, |caps: &regex::Captures| {
                let content = &caps[1];
                format!("{}{}{}",
                    SetForegroundColor(BOLD_COLOR),
                    content,
                    ResetColor
                )
            }).to_string();
            processed_item = inline_code_regex.replace_all(&processed_item, |caps: &regex::Captures| {
                let content = &caps[1];
                format!("{}{}{}",
                    SetForegroundColor(INLINE_CODE_COLOR),
                    content,
                    ResetColor
                )
            }).to_string();

            // Format as a nice list item with a bullet
            let formatted_line = format!("{}• {}", indent, processed_item);

            result.push(ProcessedLine::Regular {
                content: formatted_line,
            });
        } else {
            let mut processed_line = line.to_string();
            processed_line = bold_regex.replace_all(&processed_line, |caps: &regex::Captures| {
                let content = &caps[1];
                format!("{}{}{}",
                    SetForegroundColor(BOLD_COLOR),
                    content,
                    ResetColor
                )
            }).to_string();
            processed_line = inline_code_regex.replace_all(&processed_line, |caps: &regex::Captures| {
                let content = &caps[1];
                format!("{}{}{}",
                    SetForegroundColor(INLINE_CODE_COLOR),
                    content,
                    ResetColor
                )
            }).to_string();
            result.push(ProcessedLine::Regular {
                content: processed_line,
            });
        }
    }

    result
}

pub fn print_ai_message(app_msg: &AppMessage) -> io::Result<()> {
    let mut out = stdout(); // Use stdout for more control with crossterm
    let (terminal_width, _) = crossterm::terminal::size().unwrap_or((80, 24)); // Default width 80

    crossterm::execute!(out, Print("\n"))?; // Initial newline for AI message block

    // Add 2-space left margin for all AI messages
    let outer_left_margin = "  ";
    let outer_right_margin = "  ";
    // Calculate the width available for content *between* the outer margins
    let content_wrap_width = terminal_width
        .saturating_sub(outer_left_margin.len() as u16 + outer_right_margin.len() as u16);

    // Ensure content_wrap_width is at least 1 to avoid panic with textwrap
    let content_wrap_width = if content_wrap_width < 1 {
        1
    } else {
        content_wrap_width
    };

    for (idx, part) in app_msg.parts.iter().enumerate() {
        if idx > 0 {
            let prev_part = &app_msg.parts[idx - 1];
            let prev_ended_with_newline = match prev_part {
                MessageContent::Text(t) => t.is_empty() || t.ends_with('\n'),
                MessageContent::CodeBlock { .. } | MessageContent::ToolCall(_) => true,
            };
            if !prev_ended_with_newline {
                crossterm::execute!(out, Print("\n"))?;
            }
        }

        match part {
            MessageContent::Text(text_content) => {
                print_text_content(
                    &mut out,
                    text_content,
                    outer_left_margin,
                    outer_right_margin,
                    content_wrap_width,
                )?;
            }
            MessageContent::CodeBlock { language, content } => {
                print_code_block(&mut out, language, content, terminal_width)?;
            }
            MessageContent::ToolCall(tool_call) => {
                print_tool_call(&mut out, tool_call, outer_left_margin, outer_right_margin)?;
            }
        }
    }
    // We already add newlines after each content type, so we don't need an extra one here

    out.flush() // Ensure all buffered output is written
}

fn print_text_content(
    out: &mut io::Stdout,
    text_content: &str,
    outer_left_margin: &str,
    outer_right_margin: &str,
    content_wrap_width: u16,
) -> io::Result<()> {
    if text_content.is_empty() {
        return Ok(());
    }

    // Process the text content with markdown formatting
    let processed_lines = process_markdown_text(text_content, content_wrap_width);

    for line_info in processed_lines {
        match line_info {
            ProcessedLine::Heading { content, centered } => {
                if centered {
                    // Center the heading within the available width
                    // Note: measure_text_width accounts for ANSI escape codes
                    let heading_width = measure_text_width(&content);
                    let available_width = content_wrap_width as usize;

                    if heading_width < available_width {
                        let padding = (available_width - heading_width) / 2;
                        let padding_str = " ".repeat(padding);
                        crossterm::execute!(
                            out,
                            Print(format!(
                                "{}{}{}{}\n",
                                outer_left_margin, padding_str, content, outer_right_margin
                            ))
                        )?;
                    } else {
                        // If heading is too long, just print it normally
                        crossterm::execute!(
                            out,
                            Print(format!(
                                "{}{}{}\n",
                                outer_left_margin, content, outer_right_margin
                            ))
                        )?;
                    }
                } else {
                    crossterm::execute!(
                        out,
                        Print(format!(
                            "{}{}{}\n",
                            outer_left_margin, content, outer_right_margin
                        ))
                    )?;
                }
            }
            ProcessedLine::Regular { content } => {
                if content.trim().is_empty() && !content.is_empty() {
                    // Preserve lines that were just spaces
                    crossterm::execute!(
                        out,
                        Print(format!(
                            "{}{}{}\n",
                            outer_left_margin, content, outer_right_margin
                        ))
                    )?;
                } else if content.is_empty() {
                    // Preserve completely empty lines
                    crossterm::execute!(
                        out,
                        Print(format!(
                            "{}{}{}\n",
                            outer_left_margin, "", outer_right_margin
                        ))
                    )?;
                } else {
                    // Wrap long lines
                    let options = Options::new(content_wrap_width as usize)
                        .word_separator(textwrap::WordSeparator::AsciiSpace)
                        .subsequent_indent("")
                        .initial_indent("");

                    let wrapped_lines = textwrap::wrap(&content, &options);
                    for wrapped_line in wrapped_lines {
                        crossterm::execute!(
                            out,
                            Print(format!(
                                "{}{}{}\n",
                                outer_left_margin, wrapped_line, outer_right_margin
                            ))
                        )?;
                    }
                }
            }
        }
    }

    // Add a newline after text blocks for consistent spacing
    crossterm::execute!(out, Print("\n"))?;
    Ok(())
}

fn print_code_block(
    out: &mut io::Stdout,
    language: &Option<String>,
    content: &str,
    terminal_width: u16,
) -> io::Result<()> {
    // Top padding line for the code block (full width background)
    crossterm::execute!(
        out,
        SetBackgroundColor(CODE_BLOCK_BACKGROUND),
        Print(" ".repeat(terminal_width as usize)),
        Print("\n"), // Newline after the padding line
        ResetColor   // Reset immediately so only this line has this specific full background
    )?;

    let highlighted_code_full =
        syntax_highlighting::highlight_code_to_ansi_string(content, language.as_deref());

    // Margins INSIDE the code block's background
    let inner_left_margin = "  "; // These are the required 2 spaces
    let inner_right_margin = "  ";

    // Width available for code *between* the inner_left_margin and inner_right_margin
    let code_content_wrap_width = terminal_width
        .saturating_sub(inner_left_margin.len() as u16 + inner_right_margin.len() as u16);

    // Ensure code_content_wrap_width is at least 1
    let code_content_wrap_width = if code_content_wrap_width < 1 {
        1
    } else {
        code_content_wrap_width
    };

    // Configure textwrap for ANSI-highlighted code
    // We need special handling for ANSI escape codes
    let options = create_code_wrap_options(code_content_wrap_width);

    if content.is_empty() {
        // Handle empty code block: print a line with inner margins and background
        print_empty_code_line(
            out,
            &CODE_BLOCK_BACKGROUND,
            inner_left_margin,
            inner_right_margin,
            code_content_wrap_width,
        )?;
    } else {
        for original_line in highlighted_code_full.lines() {
            if original_line.is_empty() {
                // Handle empty lines within the code
                print_empty_code_line(
                    out,
                    &CODE_BLOCK_BACKGROUND,
                    inner_left_margin,
                    inner_right_margin,
                    code_content_wrap_width,
                )?;
                continue;
            }
            let wrapped_segments = textwrap::wrap(original_line, &options);
            for segment in wrapped_segments {
                print_code_line(
                    out,
                    &segment,
                    &CODE_BLOCK_BACKGROUND,
                    inner_left_margin,
                    inner_right_margin,
                    terminal_width,
                )?;
            }
        }
    }

    // Bottom padding line for the code block (full width background)
    crossterm::execute!(
        out,
        SetBackgroundColor(CODE_BLOCK_BACKGROUND),
        Print(" ".repeat(terminal_width as usize)),
        Print("\n"), // Newline after the padding line
        ResetColor
    )?;

    // Always add a newline after code blocks for better visual separation
    crossterm::execute!(out, Print("\n"))?;
    Ok(())
}

fn create_code_wrap_options(code_content_wrap_width: u16) -> Options<'static> {
    Options::new(code_content_wrap_width as usize)
        // Use a custom word splitter that preserves ANSI escape codes
        .word_splitter(WordSplitter::Custom(|word| {
            // Simple implementation that splits at reasonable points
            // but avoids splitting inside ANSI escape sequences
            let mut splits = Vec::new();
            let mut in_ansi = false;
            let mut last_split = 0;

            for (i, c) in word.char_indices() {
                if c == '\x1B' {
                    // ESC character
                    in_ansi = true;
                } else if in_ansi && c == 'm' {
                    in_ansi = false;
                } else if !in_ansi && (c == '_' || c == '-') {
                    // Split after hyphens and underscores when not in ANSI sequence
                    splits.push(i + 1);
                    last_split = i + 1;
                }
            }

            // If the word is very long and we haven't found any good split points,
            // add some artificial ones every 10 characters, but not in ANSI sequences
            if splits.is_empty() && word.len() > 20 {
                in_ansi = false;
                for (i, c) in word.char_indices() {
                    if c == '\x1B' {
                        // ESC character
                        in_ansi = true;
                    } else if in_ansi && c == 'm' {
                        in_ansi = false;
                    } else if !in_ansi && i > last_split + 10 {
                        splits.push(i);
                        last_split = i;
                    }
                }
            }

            splits
        }))
        .break_words(true) // Break long tokens if they don't fit
}

fn print_empty_code_line(
    out: &mut io::Stdout,
    code_block_bg_color: &Color,
    inner_left_margin: &str,
    inner_right_margin: &str,
    code_content_wrap_width: u16,
) -> io::Result<()> {
    crossterm::execute!(
        out,
        SetBackgroundColor(*code_block_bg_color),
        Print(inner_left_margin),
        Print(" ".repeat(code_content_wrap_width as usize)), // Fill the wrap width
        Print(inner_right_margin),
        ResetColor,
        Print("\n")
    )
}

fn print_code_line(
    out: &mut io::Stdout,
    segment: &str,
    code_block_bg_color: &Color,
    inner_left_margin: &str,
    inner_right_margin: &str,
    terminal_width: u16,
) -> io::Result<()> {
    crossterm::execute!(out, SetBackgroundColor(*code_block_bg_color))?;
    crossterm::execute!(out, Print(inner_left_margin))?;
    crossterm::execute!(out, Print(segment))?; // segment already has ANSI
    crossterm::execute!(out, Print(inner_right_margin))?;

    // Calculate padding to fill the rest of the terminal line
    let visible_width_of_segment_with_margins = inner_left_margin.len() +
        measure_text_width(segment) + // measure_text_width is ANSI-aware
        inner_right_margin.len();

    let line_padding_len =
        terminal_width.saturating_sub(visible_width_of_segment_with_margins as u16);
    if line_padding_len > 0 {
        crossterm::execute!(out, Print(" ".repeat(line_padding_len as usize)))?;
    }

    crossterm::execute!(out, ResetColor, Print("\n"))
}

fn print_tool_call(
    out: &mut io::Stdout,
    tool_call: &crate::llm_client::ToolCallAction,
    outer_left_margin: &str,
    outer_right_margin: &str,
) -> io::Result<()> {
    // Format tool call in compact format: └── Calling tool_name: {"arg": "value"...
    let args_json = match serde_json::to_string(&tool_call.arguments) {
        Ok(json) => {
            // Truncate after 200 characters
            if json.len() > 200 {
                format!("{}...", &json[..200])
            } else {
                json
            }
        }
        Err(_) => "{}".to_string(),
    };

    let tool_call_line = format!("└── Calling {}: {}", tool_call.tool_name, args_json);

    crossterm::execute!(
        out,
        Print(outer_left_margin),
        Print(tool_call_line.cyan().bold()),
        Print(outer_right_margin),
        Print("\n")
    )?;

    // Always add a newline after tool calls for better visual separation
    crossterm::execute!(out, Print("\n"))
}
